# Service: Tests (TS)

Ce dépôt contient tous les tests qualité automatisés du projet.
Les tests sont exécutés à chaque push et une fois par heure.

Avec VitestNotify, l'état des tests est synchronisé sur Notion et des notifications sont envoyées sur un channel Telegram quand un test échoue.

## Installation

```bash
yarn
```

## Utilisation

Sans interface graphique:

```bash
yarn test
```

Avec l'interface graphique ([localhost:51204/\_\_vitest\_\_](http://localhost:51204/__vitest__/)):

```bash
yarn test:ui
```

## Création d'un test

1. Lancer Vitest avec `yarn test` ou `yarn test:ui`.
2. Créer un fichier `*.test.ts` dans un dossier.
3. Écrire un/des test(s) compatible(s) avec Vitest/Jest.
4. Commit et push (GitHub Actions se chargera de déployer les tests sur EKS)
5. Aller sur Notion pour voir les résultats.

## Tests en local uniquement

Pour les tests temporaires et personnels (par exemple pour tester l'implémentation d'une nouvelle feature ou d'un fix), il est possible de les garder en local uniquement en les plaçant dans le dossier `local/` à la racine du dépôt.

## Nommer ses tests

L'idéal est de nommer ses tests en fonction de la feature qu'ils testent, par exemple `TSK-374_noMatchingOnSourceField.test.ts` pour tester la feature décrite dans la tâche notion `TSK-374` qui consiste à ne plus avoir de champ `matchedProps.*.source` dans les documents de la collection `matching`.
