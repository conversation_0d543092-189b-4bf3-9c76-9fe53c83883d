import { it, expect, describe } from 'vitest';
import { db } from '../lib/mongo';

describe('BestScores Cache', () => {
  it('should be less than 30 minutes old', async () => {
    const deprecatedDoc = await db
      .collection('scoring_BestScores_Cache')
      .findOne({
        _time: { $exists: true },
        $expr: {
          $lt: [
            '$_time',
            {
              $dateSubtract: {
                startDate: '$$NOW',
                unit: 'minute',
                amount: 30,
              },
            },
          ],
        },
      });

    const deprecatedDocExists = !!deprecatedDoc && !!deprecatedDoc._id;

    console.log('Results:', { deprecatedDocExists });

    expect(deprecatedDocExists).toBe(false);
  });
});
