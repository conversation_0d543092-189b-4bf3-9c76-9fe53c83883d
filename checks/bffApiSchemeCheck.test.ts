import { describe, it, expect } from 'vitest';
import axios, { AxiosResponse } from 'axios';

const urls = {
  ethereum:
    'https://bff-pdf.lesscoresdechantal.com/v1/telegram/coins?name=ethereum',
  ETH: 'https://bff-pdf.lesscoresdechantal.com/v1/telegram/coins?symbol=ETH',
};

for (const [name, url] of Object.entries(urls)) {
  describe(`Url with: "${name}"`, () => {
    let result: AxiosResponse;

    it('should return a 200 status code', async () => {
      result = await axios.get(url);
      expect(result.status).toBe(200);
    }, 30000);

    it('should have a charts.prices array length > 10', () => {
      expect(result.data.charts.prices.length).toBeGreaterThan(10);
    });

    it('should have a links array containing { name: "Coingecko", url: something }', () => {
      expect(result.data.links).toContainEqual({
        name: '<PERSON><PERSON>ck<PERSON>',
        url: expect.anything(),
      });
    });
  });
}
