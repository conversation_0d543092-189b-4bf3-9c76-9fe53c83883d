import { describe, it, expect } from 'vitest';
import axios, { AxiosResponse } from 'axios';

const urls = {
  'api/coins-1': 'https://api.lesscoresdechantal.com/blockunity/v1/coins-1',
};

const MAX_ATTEMPTS = 3;
const TIMEOUT = 20000;
const RETRY_DELAY = 5000;

async function retryAxios<T>(url: string, headers?: Record<string, string>) {
  for (let attempt = 1; attempt <= MAX_ATTEMPTS; attempt += 1) {
    try {
      const result = <AxiosResponse<T>>await Promise.race([
        axios.get(url, { headers }),
        new Promise((_, reject) => {
          setTimeout(reject, TIMEOUT);
        }),
      ]);

      return result;
    } catch {
      if (attempt === MAX_ATTEMPTS) {
        throw new Error(
          `Failed after ${MAX_ATTEMPTS} attempts (Timeout: ${TIMEOUT}ms)`,
        );
      }
      console.log(`Attempt ${attempt} failed. Retrying in ${RETRY_DELAY}ms...`);
      await new Promise((resolve) => {
        setTimeout(resolve, RETRY_DELAY);
      });
    }
  }

  return null;
}

for (const [name, url] of Object.entries(urls)) {
  describe(`Url with: "${name}"`, () => {
    let result: AxiosResponse<
      {
        symbol: string;
        cgId: string;
        cmcId: number;
        name: string;
      }[]
    >;

    const headers = name.startsWith('api/')
      ? { 'x-api-key': process.env.BLOCKUNITY_API_KEY }
      : undefined;

    it(
      'should return a 200 status code',
      async () => {
        result = await retryAxios(url, headers);

        expect(result).toBeDefined();
        expect(result.status).toBe(200);
      },
      { timeout: (MAX_ATTEMPTS + 1) * (TIMEOUT + RETRY_DELAY) },
    );

    it('should have a coins array length > 7000', () => {
      expect(result).toBeDefined();
      expect(result.data).toBeInstanceOf(Array);
      expect(result.data.length).toBeGreaterThan(7000);
    });

    it('should contain a bitcoin coin', async () => {
      const expectedBitcoinFields = <const>{
        symbol: 'btc',
        cgId: 'bitcoin',
        cmcId: 1,
        name: 'Bitcoin',
      };

      const bitcoinDoc = result.data.find(
        (coin) => coin.cgId === expectedBitcoinFields.cgId,
      );

      expect(bitcoinDoc, 'bitcoinDoc').toBeDefined();
      expect(bitcoinDoc, 'bitcoinDoc').toContain(expectedBitcoinFields);
    });
  });
}
