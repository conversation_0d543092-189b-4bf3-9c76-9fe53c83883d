import { it, expect, describe } from 'vitest';
import { db } from '../lib/mongo';

/**
 * Specify a timeout in hours for specific collections
 * that need a different value than the default one (27h)
 */
const COLLECTION_ALERT_THRESHOLD = <const>{
  // La collecte des données de CoinGeckoMarkets
  // est effectuée toutes les heures
  collect_CoingeckoMarkets: 3,

  // Les 4 collectes suivantes sont des documents
  // Google Sheets qui sont mis à jour manuellement
  collect_TokenMetricsReviews: 4 * 24,
  collect_News: 7 * 24,
  collect_AchatsChantal: 7 * 24,
  collect_ChantalNotes: 7 * 24,

  // On désactive l'alerte pour la collect ManualMatching
  // car elle n'est pas souvent mise à jour
  collect_ManualMatching: Infinity,
};

/**
 * Specify required fields for specific collections
 */
const COLLECT_REQUIRED_FIELDS: Record<
  string,
  { fieldName: string; minimumCount: number }[]
> = {
  SkynetCertik: [{ fieldName: 'symbol', minimumCount: 3000 }],
};

type CollectionName = keyof typeof COLLECTION_ALERT_THRESHOLD;

function getCollectionAlertThreshold(collectionName: CollectionName) {
  return COLLECTION_ALERT_THRESHOLD[collectionName] || 27;
}

async function getCollects() {
  return db
    .listCollections({
      name: { $regex: /^collect_(?!.*_test).*$/ },
      type: 'collection',
    })
    .map(({ name }: { name: CollectionName }) => name)
    .toArray();
}

for (const collect of await getCollects()) {
  // Create index if not exists
  await db.collection(collect).createIndex({ _time: 1 });

  const collectName = collect.replace(/^collect_/, '');

  describe(`Collect: '${collectName}'`, async () => {
    const noTimeDocs = await db
      .collection(collect)
      .aggregate([
        {
          $match: {
            _time: { $exists: false },
          },
        },
      ])
      .limit(9999)
      .toArray();

    it('should have no document without _time field', async () => {
      console.log('Results:', {
        documents: noTimeDocs,
        collect,
        count: noTimeDocs.length,
      });

      expect(noTimeDocs).toHaveLength(0);
    });

    const collectionAlertThreshold = getCollectionAlertThreshold(collect);

    if (Number.isFinite(collectionAlertThreshold))
      it.skipIf(noTimeDocs.length > 0)(
        `should have at least one document less than ${collectionAlertThreshold}h`,
        async () => {
          const documents = await db
            .collection(collect)
            .aggregate([
              {
                $match: {
                  $expr: {
                    $gt: [
                      '$_time',
                      {
                        $dateSubtract: {
                          startDate: '$$NOW',
                          unit: 'hour',
                          amount: collectionAlertThreshold,
                        },
                      },
                    ],
                  },
                },
              },
            ])
            .limit(1)
            .toArray();

          console.log('Results:', {
            documents,
            collect,
            count: documents.length,
          });

          expect(documents).length.greaterThan(0);
        },
      );

    for (const requiredField of COLLECT_REQUIRED_FIELDS[collectName] ?? []) {
      const { fieldName, minimumCount } = requiredField;

      it.skipIf(noTimeDocs.length > 0)(
        `should have less than ${minimumCount} documents without '${fieldName}' field`,
        async () => {
          const documents = await db
            .collection(collect)
            .aggregate([
              {
                $match: {
                  symbol: null,
                },
              },
            ])
            .toArray();

          console.log('Results:', {
            documents,
            collect,
            count: documents.length,
          });

          expect(documents).length.lessThan(1000);
        },
      );
    }
  });
}
