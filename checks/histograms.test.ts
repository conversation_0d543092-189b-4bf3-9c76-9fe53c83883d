import { it, expect, describe } from 'vitest';
import { db } from '../lib/mongo';

describe('Histograms collection', () => {
  it('should have no >27h document', async () => {
    const histogramsDocs = await getOldDocuments();
    expect(histogramsDocs, 'histogramsDocs').toHaveLength(0);
  });

  it('should have no errored document', async () => {
    const erroredDocs = await getErroredDocuments();
    expect(erroredDocs, 'erroredDocs').toHaveLength(0);
  });
});

function getOldDocuments() {
  return db
    .collection('histograms')
    .find({
      type: { $ne: 'template' },
      $expr: {
        $lt: [
          '$_time',
          {
            $dateSubtract: {
              startDate: '$$NOW',
              unit: 'hour',
              amount: 27,
            },
          },
        ],
      },
    })
    .toArray();
}

function getErroredDocuments() {
  return db.collection('histograms').find({ status: 'ERROR' }).toArray();
}
