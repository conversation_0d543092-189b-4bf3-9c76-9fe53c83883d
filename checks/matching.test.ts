import { it, expect, describe } from 'vitest';
import { db } from '../lib/mongo';

function getSources() {
  return db
    .collection('matching')
    .aggregate([
      {
        $project: {
          _times: {
            $objectToArray: '$_times',
          },
        },
      },
      {
        $unwind: '$_times',
      },
      {
        $group: {
          _id: '$_times.k',
        },
      },
    ])
    .toArray();
}

for (const source of await getSources()) {
  describe(`Matching: '${source._id}'`, () => {
    it('should have no document with the _times.* field older than 27h', async () => {
      const documents = await db
        .collection('matching')
        .aggregate([
          {
            $project: {
              _time: `$_times.${source._id}`,
            },
          },
          {
            $match: {
              _time: { $exists: true },
              $expr: {
                $lt: [
                  '$_time',
                  {
                    $subtract: ['$$NOW', 1000 * 60 * 60 * 27],
                  },
                ],
              },
            },
          },
        ])
        .toArray();

      console.log('Results:', {
        documents,
        source: source._id,
        count: documents.length,
      });

      expect(documents).toHaveLength(0);
    });
  });
}
