import { it, expect, describe } from 'vitest';
import { dbDatacollector } from '../lib/mongo';

describe('X accounts available', () => {
  it('should have more than 3,000 available X accounts (cashtags)', async () => {
    const minCount = 3000;
    const availableAccountsCount = await getAvailableAccountsCount();
    expect(availableAccountsCount, 'availableAccountsCount').toBeGreaterThan(
      minCount,
    );
  });

  it('should have more than 1,000 available X accounts (profiles)', async () => {
    const minCount = 1000;
    const availableAccountsCount = await getAvailableAccountsCount('profile');
    expect(availableAccountsCount, 'availableAccountsCount').toBeGreaterThan(
      minCount,
    );
  });

  const COLLECTION = 'x-accounts';

  function getAvailableAccountsCount(purpose: string | null = null) {
    return dbDatacollector.collection(COLLECTION).countDocuments({
      status: 'available',
      ct0: { $ne: null },
      purpose,
    });
  }
});
