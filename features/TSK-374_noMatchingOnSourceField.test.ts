import { it, expect } from 'vitest';
import { db } from '../lib/mongo';

it('should have no document with matchedProps.*.source field', async () => {
  const documents = await db
    .collection('matching')
    .aggregate([
      {
        $match: {
          $expr: {
            $eq: [
              {
                $function: {
                  body: function checkMatchedPropsSource(matchedProps: {
                    [x: string]: { source: undefined };
                  }) {
                    return Object.keys(matchedProps).some(
                      (key) => matchedProps[key].source !== undefined,
                    );
                  }.toString(),
                  args: ['$matchedProps'],
                  lang: 'js',
                },
              },
              true,
            ],
          },
        },
      },
    ])
    .toArray();

  console.log('Results:', { documents, count: documents.length });
  expect(documents).toHaveLength(0);
});
