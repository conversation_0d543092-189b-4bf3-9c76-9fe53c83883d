import { MongoClient } from 'mongodb';

if (!process.env.MONGO_URL) {
  throw new Error('Missing MONGO_URL');
}

if (!process.env.MONGO_DB) {
  throw new Error('Missing MONGO_DB');
}

export const client = new MongoClient(process.env.MONGO_URL, {
  readPreference: 'secondaryPreferred',
});
export const db = client.db(process.env.MONGO_DB);
export const dbDatacollector = client.db(
  process.env.MONGO_DB_DATACOLLECTOR ?? 'datacollector',
);
