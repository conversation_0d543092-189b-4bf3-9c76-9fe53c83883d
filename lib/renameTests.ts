/* eslint-disable @typescript-eslint/no-var-requires */
// eslint-disable-next-line import/no-extraneous-dependencies
const notion = require('@notionhq/client');
const dotenv = require('dotenv');

dotenv.config();

/**
 * CONFIGURATION A MODIFIER AVANT UTILISATION
 */
const RULES = [
  {
    search:
      /Collect: '(News)' > should have at least one document less than 96h/,
    replace: "Collect: '$1' > should have at least one document less than 168h",
  },
  {
    search:
      /Collect: '(AchatsChantal)' > should have at least one document less than 96h/,
    replace: "Collect: '$1' > should have at least one document less than 168h",
  },
  {
    search:
      /Collect: '(ChantalNotes)' > should have at least one document less than 27h/,
    replace: "Collect: '$1' > should have at least one document less than 168h",
  },
] satisfies { search: RegExp; replace: string }[];

if (!process.env.NOTION_KEY) throw new Error('NOTION_KEY is not defined');
if (!process.env.NOTION_TESTS_DB)
  throw new Error('NOTION_TESTS_DB is not defined');

const CONFIRM = process.argv[2];

const notionClient = new notion.Client({
  auth: process.env.NOTION_KEY,
});

const databaseId = <string>process.env.NOTION_TESTS_DB;

interface NotionQueryResponse {
  object: string;
  results: {
    id: string;
    properties: { Nom: { title: { plain_text: string }[] } };
  }[];
  has_more: boolean;
  next_cursor: string;
}

async function getTests(
  start_cursor?: string,
): Promise<NotionQueryResponse['results']> {
  const response = (await notionClient.databases.query({
    database_id: databaseId,
    start_cursor,
  })) as unknown as NotionQueryResponse;

  if (!response.has_more) return response.results;
  return response.results.concat(await getTests(response.next_cursor));
}

(async () => {
  const tests = await getTests();
  console.log(tests.length, 'tests trouvés');

  console.log('Recherche des tests à modifier:');
  console.log(RULES);

  const changes = tests
    .map((test) => {
      const { id } = test;
      const title = <string>test?.properties.Nom.title[0].plain_text;
      const newTitle = RULES.reduce((acc, rule) => {
        return acc.replace(rule.search, rule.replace);
      }, title);

      if (newTitle !== title) return { id, title, newTitle };
      return null;
    })
    .filter(Boolean);

  if (!changes.length) {
    console.log('Aucun changement à faire');
    return;
  }

  console.log('Modifications de', changes.length, 'noms de tests:');

  const CONSOLE_RED = '\x1b[31m';
  const CONSOLE_GREEN = '\x1b[32m';
  const CONSOLE_RESET = '\x1b[0m';

  for (const change of changes) {
    if (!change) continue;
    console.log(CONSOLE_RESET, '', change.id);
    console.log(CONSOLE_RED, '  ', change.title);
    console.log(CONSOLE_GREEN, '  ', change.newTitle);
  }

  console.log(CONSOLE_RESET);

  const neededConfirmation = `${Math.round(Date.now() / 10 ** 6)}-${changes.length}`;

  if (CONFIRM !== neededConfirmation) {
    console.log(
      `Pour confirmer, faites la commande: 'yarn renametests ${neededConfirmation}'`,
    );
    return;
  }

  console.log('Confirmation reçue, modification des tests');

  // On vérifie s'il y a deux nouveaux tests avec le même nom
  if (
    new Set(changes.map((change) => change?.newTitle)).size !== changes.length
  ) {
    console.error(
      'Impossible de modifier les tests, deux tests ont le même nom',
    );
    process.exit(1);
  }

  for (const change of changes) {
    if (!change) continue;
    console.log('Modification de', change.id);

    await notionClient.pages.update({
      page_id: change.id,
      properties: {
        Nom: {
          title: [
            {
              text: {
                content: change.newTitle,
              },
            },
          ],
        },
      },
    });
  }

  console.log('Modifications terminées');
})();
