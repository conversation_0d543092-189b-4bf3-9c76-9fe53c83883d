{"name": "service-tests-ts", "version": "1.0.0", "repository": "https://github.com/LesScoresDeChantal/SERVICE-Tests-ts", "author": "<PERSON><PERSON> <<EMAIL>>", "license": "MIT", "private": true, "scripts": {"test": "vitest", "test:ui": "vitest --ui", "lint": "eslint .", "lint:fix": "eslint . --fix", "delete-eks-secrets": "kubectl -n apps delete secret tests-runner-secrets", "create-eks-secrets": "kubectl -n apps create secret generic tests-runner-secrets --from-env-file=.env.eks", "renametests": "ts-node ./lib/renameTests.ts"}, "dependencies": {"@vitest/ui": "^0.31.1", "axios": "^1.4.0", "dotenv": "^16.0.3", "mongodb": "^6.8.0", "surge-fstream-ignore": "^1.0.6", "tarr": "^1.1.0", "vitest": "^0.31.1", "vitest-notify": "^0.1.8"}, "devDependencies": {"@notionhq/client": "^2.2.15", "@typescript-eslint/eslint-plugin": "^7.6.0", "@typescript-eslint/parser": "^7.6.0", "eslint": "^8.53.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-airbnb-typescript": "^18.0.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-prettier": "^5.1.3", "nodemon": "^3.0.1", "prettier": "^3.1.1", "ts-node": "^10.9.2", "typescript": "^5.4.5"}}